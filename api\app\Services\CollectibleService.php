<?php

namespace App\Services;

use App\Models\Collectible;
use App\Models\CollectibleTemplate;
use App\Models\CollectionSet;
use App\Models\UserCollectionProgress;
use App\Models\TelegramUser;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CollectibleService
{
    /**
     * Unlock a collectible for a user
     */
    public function unlockCollectible(
        TelegramUser $user,
        string $collectibleId,
        string $unlockSource,
        ?string $sourceReference = null
    ): ?array {
        $collectibleTemplate = CollectibleTemplate::where('collectible_id', $collectibleId)->first();
        
        if (!$collectibleTemplate) {
            Log::warning('Collectible template not found', ['collectible_id' => $collectibleId]);
            return null;
        }

        // Check if user already owns this collectible
        $existingCollectible = $user->collectibles()
            ->where('collectible_id', $collectibleId)
            ->first();

        if ($existingCollectible) {
            return null; // Already owned
        }

        DB::beginTransaction();
        
        try {
            // Create collectible for user
            $collectible = $user->collectibles()->create([
                'collectible_id' => $collectibleId,
                'unlock_source' => $unlockSource,
                'source_reference' => $sourceReference,
                'obtained_at' => now()
            ]);

            // Update collection progress
            $this->updateCollectionProgress($user, $collectibleTemplate);

            // Check for collection completion
            $completionRewards = $this->checkCollectionCompletion($user, $collectibleTemplate);

            DB::commit();

            Log::info('Collectible unlocked', [
                'user_id' => $user->id,
                'collectible_id' => $collectibleId,
                'unlock_source' => $unlockSource
            ]);

            return [
                'collectible' => $collectible,
                'template' => $collectibleTemplate,
                'completion_rewards' => $completionRewards
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to unlock collectible', [
                'user_id' => $user->id,
                'collectible_id' => $collectibleId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get user's collection progress for all sets
     */
    public function getUserCollectionProgress(TelegramUser $user): array
    {
        $collectionSets = CollectionSet::active()->ordered()->get();
        $progress = [];

        foreach ($collectionSets as $set) {
            $userProgress = UserCollectionProgress::firstOrCreate(
                [
                    'telegram_user_id' => $user->id,
                    'set_id' => $set->set_id
                ],
                [
                    'collectibles_owned' => 0,
                    'total_collectibles' => $set->total_collectibles,
                    'completion_percentage' => 0,
                    'owned_collectible_ids' => [],
                    'missing_collectible_ids' => []
                ]
            );

            // Update progress
            $userProgress->updateProgress();
            
            $progress[] = $userProgress->getDetailedProgress();
        }

        return $progress;
    }

    /**
     * Claim collection completion rewards
     */
    public function claimCollectionRewards(TelegramUser $user, CollectionSet $collectionSet): array
    {
        $userProgress = UserCollectionProgress::where([
            'telegram_user_id' => $user->id,
            'set_id' => $collectionSet->set_id
        ])->first();

        if (!$userProgress) {
            throw new \Exception('Collection progress not found');
        }

        if (!$userProgress->is_completed) {
            throw new \Exception('Collection not completed');
        }

        if ($userProgress->rewards_claimed) {
            throw new \Exception('Rewards already claimed');
        }

        DB::beginTransaction();
        
        try {
            $rewards = $collectionSet->awardCompletionRewards($user);

            $userProgress->rewards_claimed = true;
            $userProgress->rewards_claimed_at = now();
            $userProgress->save();

            // Unlock bonus mystery box if available
            if ($collectionSet->bonus_mystery_box_type) {
                $mysteryBoxService = app(MysteryBoxService::class);
                $mysteryBoxService->unlockBoxType(
                    $user,
                    $collectionSet->bonus_mystery_box_type,
                    'collection_bonus',
                    $collectionSet->set_id
                );
            }

            DB::commit();

            Log::info('Collection rewards claimed', [
                'user_id' => $user->id,
                'collection_set_id' => $collectionSet->set_id,
                'rewards' => $rewards
            ]);

            return $rewards;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to claim collection rewards', [
                'user_id' => $user->id,
                'collection_set_id' => $collectionSet->set_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get collectibles by category for user
     */
    public function getUserCollectiblesByCategory(TelegramUser $user, string $category): array
    {
        $collectibles = $user->collectibles()
            ->whereHas('template', function($q) use ($category) {
                $q->where('category', $category);
            })
            ->with('template')
            ->orderBy('obtained_at', 'desc')
            ->get();

        return $collectibles->map(function($collectible) {
            return [
                'collectible_id' => $collectible->collectible_id,
                'name' => $collectible->template->name,
                'type' => $collectible->template->type,
                'rarity' => $collectible->template->rarity,
                'image_url' => $collectible->template->image_url,
                'obtained_at' => $collectible->obtained_at,
                'unlock_source' => $collectible->unlock_source,
                'days_owned' => $collectible->days_owned
            ];
        })->toArray();
    }

    /**
     * Get collectibles by rarity for user
     */
    public function getUserCollectiblesByRarity(TelegramUser $user, string $rarity): array
    {
        $collectibles = $user->collectibles()
            ->whereHas('template', function($q) use ($rarity) {
                $q->where('rarity', $rarity);
            })
            ->with('template')
            ->orderBy('obtained_at', 'desc')
            ->get();

        return $collectibles->map(function($collectible) {
            return [
                'collectible_id' => $collectible->collectible_id,
                'name' => $collectible->template->name,
                'type' => $collectible->template->type,
                'category' => $collectible->template->category,
                'image_url' => $collectible->template->image_url,
                'obtained_at' => $collectible->obtained_at,
                'unlock_source' => $collectible->unlock_source,
                'days_owned' => $collectible->days_owned
            ];
        })->toArray();
    }

    /**
     * Get recently obtained collectibles
     */
    public function getRecentlyObtainedCollectibles(TelegramUser $user, int $days = 7): array
    {
        $collectibles = $user->collectibles()
            ->recentlyObtained($days)
            ->with('template')
            ->orderBy('obtained_at', 'desc')
            ->get();

        return $collectibles->map(function($collectible) {
            return [
                'collectible_id' => $collectible->collectible_id,
                'name' => $collectible->template->name,
                'type' => $collectible->template->type,
                'category' => $collectible->template->category,
                'rarity' => $collectible->template->rarity,
                'image_url' => $collectible->template->image_url,
                'obtained_at' => $collectible->obtained_at,
                'unlock_source' => $collectible->unlock_source,
                'days_ago' => $collectible->obtained_at->diffInDays(now())
            ];
        })->toArray();
    }

    /**
     * Get user's collection with filtering
     */
    public function getUserCollection(
        TelegramUser $user,
        ?string $category = null,
        ?string $rarity = null,
        ?string $owned = null
    ): array {
        $query = CollectibleTemplate::active();

        if ($category) {
            $query->byCategory($category);
        }

        if ($rarity) {
            $query->byRarity($rarity);
        }

        $collectibleTemplates = $query->orderBy('collection_set_id')
                                    ->orderBy('set_position')
                                    ->get();

        $userCollectibles = $user->collectibles()->pluck('collectible_id')->toArray();

        $collection = $collectibleTemplates->map(function($template) use ($userCollectibles, $owned) {
            $isOwned = in_array($template->collectible_id, $userCollectibles);

            // Apply owned filter
            if ($owned === 'true' && !$isOwned) {
                return null;
            }
            if ($owned === 'false' && $isOwned) {
                return null;
            }

            return [
                'id' => $template->collectible_id,
                'name' => $template->name,
                'type' => $template->type,
                'rarity' => $template->rarity,
                'category' => $template->category,
                'description' => $template->description,
                'image_url' => $template->image_url,
                'collection_set_id' => $template->collection_set_id,
                'set_position' => $template->set_position,
                'is_owned' => $isOwned,
                'unlock_source' => $template->unlock_source,
                'unlock_requirement' => $template->unlock_requirement
            ];
        })->filter()->values();

        return $collection->toArray();
    }

    /**
     * Get collection sets with basic completion info
     */
    public function getCollectionSets(TelegramUser $user): array
    {
        $collectionSets = CollectionSet::active()
                                      ->orderBy('sort_order')
                                      ->orderBy('category')
                                      ->get();

        return $collectionSets->map(function($set) use ($user) {
            $progress = $this->getSetProgress($user, $set->set_id);

            return [
                'set_id' => $set->set_id,
                'name' => $set->name,
                'category' => $set->category,
                'description' => $set->description,
                'icon_url' => $set->icon_url,
                'total_collectibles' => $set->total_collectibles,
                'owned_collectibles' => $progress['owned'],
                'completion_percentage' => $progress['percentage'],
                'is_completed' => $progress['is_completed'],
                'rewards_claimed' => $progress['rewards_claimed']
            ];
        })->toArray();
    }

    /**
     * Get detailed collection sets with progress
     */
    public function getCollectionSetsDetailed(TelegramUser $user): array
    {
        $collectionSets = CollectionSet::active()
                                      ->with('collectibleTemplates')
                                      ->orderBy('sort_order')
                                      ->orderBy('category')
                                      ->get();

        return $collectionSets->map(function($set) use ($user) {
            $progress = $this->getSetProgress($user, $set->set_id);
            $collectibles = $this->getSetCollectibles($user, $set->set_id);

            return [
                'set_id' => $set->set_id,
                'name' => $set->name,
                'category' => $set->category,
                'description' => $set->description,
                'icon_url' => $set->icon_url,
                'total_collectibles' => $set->total_collectibles,
                'owned_collectibles' => $progress['owned'],
                'completion_percentage' => $progress['percentage'],
                'is_completed' => $progress['is_completed'],
                'rewards_claimed' => $progress['rewards_claimed'],
                'completion_rewards' => $set->completion_rewards,
                'bonus_mystery_box_type' => $set->bonus_mystery_box_type,
                'collectibles' => $collectibles
            ];
        })->toArray();
    }

    /**
     * Get specific collection set details
     */
    public function getCollectionSetDetails(TelegramUser $user, string $setId): array
    {
        $collectionSet = CollectionSet::where('set_id', $setId)
                                     ->where('is_active', true)
                                     ->with('collectibleTemplates')
                                     ->firstOrFail();

        $progress = $this->getSetProgress($user, $setId);
        $collectibles = $this->getSetCollectibles($user, $setId);

        return [
            'set_id' => $collectionSet->set_id,
            'name' => $collectionSet->name,
            'category' => $collectionSet->category,
            'description' => $collectionSet->description,
            'icon_url' => $collectionSet->icon_url,
            'total_collectibles' => $collectionSet->total_collectibles,
            'owned_collectibles' => $progress['owned'],
            'completion_percentage' => $progress['percentage'],
            'is_completed' => $progress['is_completed'],
            'rewards_claimed' => $progress['rewards_claimed'],
            'completion_rewards' => $collectionSet->completion_rewards,
            'bonus_mystery_box_type' => $collectionSet->bonus_mystery_box_type,
            'collectibles' => $collectibles,
            'lore' => $collectionSet->lore ?? null
        ];
    }

    /**
     * Claim collection set completion rewards
     */
    public function claimCollectionSetRewards(TelegramUser $user, string $setId): array
    {
        $collectionSet = CollectionSet::where('set_id', $setId)
                                     ->where('is_active', true)
                                     ->firstOrFail();

        $userProgress = UserCollectionProgress::where([
            'telegram_user_id' => $user->id,
            'set_id' => $setId
        ])->first();

        if (!$userProgress) {
            throw new \Exception('Collection progress not found');
        }

        if (!$userProgress->is_completed) {
            throw new \Exception('Collection set is not completed yet');
        }

        if ($userProgress->rewards_claimed) {
            throw new \Exception('Rewards have already been claimed');
        }

        DB::beginTransaction();
        try {
            $rewards = [];
            $completionRewards = $collectionSet->completion_rewards;

            // Award coins
            if (isset($completionRewards['coins']) && $completionRewards['coins'] > 0) {
                $user->increment('balance', $completionRewards['coins']);
                $rewards['coins'] = $completionRewards['coins'];
            }

            // Award gems
            if (isset($completionRewards['gems']) && $completionRewards['gems'] > 0) {
                $user->increment('gems', $completionRewards['gems']);
                $rewards['gems'] = $completionRewards['gems'];
            }

            // Award mystery box
            if ($collectionSet->bonus_mystery_box_type) {
                $rewards['mystery_box'] = $collectionSet->bonus_mystery_box_type;
            }

            // Mark rewards as claimed
            $userProgress->update([
                'rewards_claimed' => true,
                'rewards_claimed_at' => now()
            ]);

            DB::commit();

            return [
                'rewards' => $rewards,
                'set_name' => $collectionSet->name
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get collection statistics
     */
    public function getCollectionStatistics(TelegramUser $user): array
    {
        $totalCollectibles = CollectibleTemplate::active()->count();
        $ownedCollectibles = $user->collectibles()->count();

        $collectiblesByCategory = $user->collectibles()
                                      ->join('collectible_templates', 'collectibles.collectible_id', '=', 'collectible_templates.collectible_id')
                                      ->selectRaw('collectible_templates.category, COUNT(*) as count')
                                      ->groupBy('collectible_templates.category')
                                      ->pluck('count', 'category')
                                      ->toArray();

        $collectiblesByRarity = $user->collectibles()
                                    ->join('collectible_templates', 'collectibles.collectible_id', '=', 'collectible_templates.collectible_id')
                                    ->selectRaw('collectible_templates.rarity, COUNT(*) as count')
                                    ->groupBy('collectible_templates.rarity')
                                    ->pluck('count', 'rarity')
                                    ->toArray();

        $collectiblesBySource = $user->collectibles()
                                    ->selectRaw('unlock_source, COUNT(*) as count')
                                    ->groupBy('unlock_source')
                                    ->pluck('count', 'unlock_source')
                                    ->toArray();

        $completedSets = UserCollectionProgress::where('telegram_user_id', $user->id)
                                              ->where('is_completed', true)
                                              ->count();

        $totalSets = CollectionSet::active()->count();

        return [
            'total_collectibles' => $ownedCollectibles,
            'total_possible_collectibles' => $totalCollectibles,
            'collection_percentage' => $totalCollectibles > 0
                ? round(($ownedCollectibles / $totalCollectibles) * 100, 1)
                : 0,
            'collectibles_by_category' => $collectiblesByCategory,
            'collectibles_by_rarity' => $collectiblesByRarity,
            'collectibles_by_source' => $collectiblesBySource,
            'completed_sets' => $completedSets,
            'total_sets' => $totalSets,
            'set_completion_percentage' => $totalSets > 0
                ? round(($completedSets / $totalSets) * 100, 1)
                : 0
        ];
    }

    /**
     * Get collection milestones
     */
    public function getCollectionMilestones(TelegramUser $user): array
    {
        $totalCollectibles = $user->collectibles()->count();
        $completedSets = UserCollectionProgress::where('telegram_user_id', $user->id)
                                              ->where('is_completed', true)
                                              ->count();

        $milestones = [
            [
                'id' => 'first_collectible',
                'name' => 'First Collectible',
                'description' => 'Obtain your first collectible',
                'target' => 1,
                'current' => min($totalCollectibles, 1),
                'completed' => $totalCollectibles >= 1,
                'reward' => '100 coins'
            ],
            [
                'id' => 'collector_novice',
                'name' => 'Collector Novice',
                'description' => 'Collect 10 collectibles',
                'target' => 10,
                'current' => min($totalCollectibles, 10),
                'completed' => $totalCollectibles >= 10,
                'reward' => '500 coins'
            ],
            [
                'id' => 'collector_apprentice',
                'name' => 'Collector Apprentice',
                'description' => 'Collect 25 collectibles',
                'target' => 25,
                'current' => min($totalCollectibles, 25),
                'completed' => $totalCollectibles >= 25,
                'reward' => '1000 coins + 5 gems'
            ],
            [
                'id' => 'collector_expert',
                'name' => 'Collector Expert',
                'description' => 'Collect 50 collectibles',
                'target' => 50,
                'current' => min($totalCollectibles, 50),
                'completed' => $totalCollectibles >= 50,
                'reward' => '2500 coins + 10 gems'
            ],
            [
                'id' => 'collector_master',
                'name' => 'Collector Master',
                'description' => 'Collect 100 collectibles',
                'target' => 100,
                'current' => min($totalCollectibles, 100),
                'completed' => $totalCollectibles >= 100,
                'reward' => '5000 coins + 25 gems'
            ],
            [
                'id' => 'first_set_completion',
                'name' => 'Set Completionist',
                'description' => 'Complete your first collection set',
                'target' => 1,
                'current' => min($completedSets, 1),
                'completed' => $completedSets >= 1,
                'reward' => '1000 coins + Mystery Box'
            ],
            [
                'id' => 'multiple_sets',
                'name' => 'Collection Enthusiast',
                'description' => 'Complete 3 collection sets',
                'target' => 3,
                'current' => min($completedSets, 3),
                'completed' => $completedSets >= 3,
                'reward' => '3000 coins + 15 gems'
            ],
            [
                'id' => 'set_master',
                'name' => 'Set Master',
                'description' => 'Complete 5 collection sets',
                'target' => 5,
                'current' => min($completedSets, 5),
                'completed' => $completedSets >= 5,
                'reward' => '7500 coins + 30 gems + Rare Mystery Box'
            ]
        ];

        return $milestones;
    }

    /**
     * Search collectibles
     */
    public function searchCollectibles(TelegramUser $user, string $query): array
    {
        $userCollectibles = $user->collectibles()->pluck('collectible_id')->toArray();

        $collectibleTemplates = CollectibleTemplate::active()
                                                  ->where(function($q) use ($query) {
                                                      $q->where('name', 'LIKE', "%{$query}%")
                                                        ->orWhere('description', 'LIKE', "%{$query}%")
                                                        ->orWhere('category', 'LIKE', "%{$query}%")
                                                        ->orWhere('type', 'LIKE', "%{$query}%");
                                                  })
                                                  ->orderBy('name')
                                                  ->limit(20)
                                                  ->get();

        return $collectibleTemplates->map(function($template) use ($userCollectibles) {
            $isOwned = in_array($template->collectible_id, $userCollectibles);

            return [
                'id' => $template->collectible_id,
                'name' => $template->name,
                'type' => $template->type,
                'rarity' => $template->rarity,
                'category' => $template->category,
                'description' => $template->description,
                'image_url' => $template->image_url,
                'collection_set_id' => $template->collection_set_id,
                'is_owned' => $isOwned,
                'unlock_source' => $template->unlock_source
            ];
        })->toArray();
    }

    // Private helper methods

    private function updateCollectionProgress(TelegramUser $user, CollectibleTemplate $collectibleTemplate): void
    {
        $collectionSet = $collectibleTemplate->collectionSet;
        if (!$collectionSet) {
            return;
        }

        $userProgress = UserCollectionProgress::firstOrCreate(
            [
                'telegram_user_id' => $user->id,
                'set_id' => $collectionSet->set_id
            ],
            [
                'collectibles_owned' => 0,
                'total_collectibles' => $collectionSet->total_collectibles,
                'completion_percentage' => 0,
                'owned_collectible_ids' => [],
                'missing_collectible_ids' => []
            ]
        );

        $userProgress->updateProgress();
    }

    private function checkCollectionCompletion(TelegramUser $user, CollectibleTemplate $collectibleTemplate): ?array
    {
        $collectionSet = $collectibleTemplate->collectionSet;
        if (!$collectionSet) {
            return null;
        }

        $userProgress = UserCollectionProgress::where([
            'telegram_user_id' => $user->id,
            'set_id' => $collectionSet->set_id
        ])->first();

        if (!$userProgress || !$userProgress->is_completed || $userProgress->rewards_claimed) {
            return null;
        }

        // Collection just completed, return available rewards info
        return [
            'collection_completed' => true,
            'collection_name' => $collectionSet->name,
            'available_rewards' => $collectionSet->completion_rewards,
            'bonus_mystery_box' => $collectionSet->bonus_mystery_box_type
        ];
    }

    /**
     * Get set progress for a user
     */
    private function getSetProgress(TelegramUser $user, string $setId): array
    {
        $userProgress = UserCollectionProgress::where([
            'telegram_user_id' => $user->id,
            'set_id' => $setId
        ])->first();

        if (!$userProgress) {
            $collectionSet = CollectionSet::where('set_id', $setId)->first();
            return [
                'owned' => 0,
                'total' => $collectionSet ? $collectionSet->total_collectibles : 0,
                'percentage' => 0,
                'is_completed' => false,
                'rewards_claimed' => false
            ];
        }

        return [
            'owned' => $userProgress->collectibles_owned,
            'total' => $userProgress->total_collectibles,
            'percentage' => $userProgress->completion_percentage,
            'is_completed' => $userProgress->is_completed,
            'rewards_claimed' => $userProgress->rewards_claimed
        ];
    }

    /**
     * Get collectibles for a specific set
     */
    private function getSetCollectibles(TelegramUser $user, string $setId): array
    {
        $collectibleTemplates = CollectibleTemplate::where('collection_set_id', $setId)
                                                  ->where('is_active', true)
                                                  ->orderBy('set_position')
                                                  ->get();

        $userCollectibles = $user->collectibles()->pluck('collectible_id')->toArray();

        return $collectibleTemplates->map(function($template) use ($userCollectibles) {
            $isOwned = in_array($template->collectible_id, $userCollectibles);

            return [
                'id' => $template->collectible_id,
                'name' => $template->name,
                'type' => $template->type,
                'rarity' => $template->rarity,
                'category' => $template->category,
                'description' => $template->description,
                'image_url' => $template->image_url,
                'set_position' => $template->set_position,
                'is_owned' => $isOwned,
                'unlock_source' => $template->unlock_source,
                'unlock_requirement' => $template->unlock_requirement
            ];
        })->toArray();
    }
}
