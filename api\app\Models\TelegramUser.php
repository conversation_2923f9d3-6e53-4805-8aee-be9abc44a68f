<?php

namespace App\Models;

use App\Observers\TelegramUserObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Facades\DB;
use Lara<PERSON>\Sanctum\HasApiTokens;
use App\Models\TapStat;
use App\Models\UserAchievementPoint;

#[ObservedBy(TelegramUserObserver::class)]
class TelegramUser extends Authenticatable
{
    use HasApiTokens;

    protected $guarded = [];

    protected $hidden = [
        'remember_token',
    ];

    protected $casts = [
        'last_login_date' => 'datetime',
        'last_daily_booster_use' => 'datetime',
        'booster_pack_2x' => 'boolean',
        'booster_pack_3x' => 'boolean',
        'booster_pack_7x' => 'boolean',
        'tower_game_unlocked' => 'boolean',
        'tower_game_plays_reset_at' => 'datetime',
        'rabbit_game_unlocked' => 'boolean', // Added for Rabbit game
        'slash_game_unlocked' => 'boolean', // Added for Slash game
    ];

    public function referrals()
    {
        return $this->hasMany(self::class, 'referred_by', 'telegram_id');
    }

    public function dailyTasks()
    {
        return $this->belongsToMany(DailyTask::class, 'telegram_user_daily_tasks')
            ->withPivot('completed', 'created_at')
            ->withTimestamps();
    }

    public function tasks()
    {
        return $this->belongsToMany(Task::class, 'telegram_user_tasks')
            ->withPivot('is_submitted', 'is_rewarded', 'submitted_at')
            ->withTimestamps();
    }

    public function referralTasks()
    {
        return $this->belongsToMany(ReferralTask::class, 'telegram_user_referral_task')
            ->withPivot('is_completed')
            ->withTimestamps();
    }

    public function level()
    {
        return $this->belongsTo(Level::class);
    }

    // Pet System Relationships
    public function pets()
    {
        return $this->hasMany(Pet::class, 'telegram_user_id');
    }

    public function petInteractions()
    {
        return $this->hasMany(PetInteraction::class, 'telegram_user_id');
    }

    public function collectibles()
    {
        return $this->hasMany(Collectible::class, 'telegram_user_id');
    }

    public function collectionProgress()
    {
        return $this->hasMany(UserCollectionProgress::class, 'telegram_user_id');
    }

    public function mysteryBoxUnlocks()
    {
        return $this->hasMany(MysteryBoxUnlock::class, 'telegram_user_id');
    }

    public function mysteryBoxOpenings()
    {
        return $this->hasMany(MysteryBoxOpening::class, 'telegram_user_id');
    }

    // Pet System Helper Methods
    public function getFeaturedPet()
    {
        return $this->pets()->featured()->with('template')->first();
    }

    public function getPetsNeedingAttention()
    {
        return $this->pets()->needingAttention()->with('template')->get();
    }

    public function prizeTreeProgress()
    {
        // This would return the user's prize tree progress
        // Implementation depends on existing prize tree system
        return null;
    }

    /**
     * Get user's collection progress summary
     */
    public function getCollectionProgress(): array
    {
        $totalCollectibles = $this->collectibles()->count();
        $totalPossible = \App\Models\CollectibleTemplate::active()->count();

        $completedSets = $this->collectionProgress()
                             ->where('is_completed', true)
                             ->count();

        $totalSets = \App\Models\CollectionSet::active()->count();

        return [
            'total_collectibles' => $totalCollectibles,
            'total_possible_collectibles' => $totalPossible,
            'collection_percentage' => $totalPossible > 0
                ? round(($totalCollectibles / $totalPossible) * 100, 1)
                : 0,
            'completed_sets' => $completedSets,
            'total_sets' => $totalSets,
            'set_completion_percentage' => $totalSets > 0
                ? round(($completedSets / $totalSets) * 100, 1)
                : 0
        ];
    }

    public function updateLoginStreak()
    {
        if (!$this->last_login_date?->isToday()) {
            $cap = DailyTask::count();
            $lastClaimedDailyTask = $this->dailyTasks()
                ->orderBy('telegram_user_daily_tasks.created_at', 'desc')
                ->first();

            if (
                $this->last_login_date?->isYesterday()
                && $lastClaimedDailyTask?->pivot?->created_at?->isYesterday()
                && $this->login_streak !== $cap
            ) {
                $this->login_streak = min($this->login_streak + 1, $cap);
            } else {
                $this->login_streak = 1;
                $this->dailyTasks()->detach();
            }
        }
    }

    public function calcPassiveEarning()
    {
        $passiveEarnings = 0;
        if ($this->last_login_date && $this->production_per_hour) {
            $threeHours = 3 * 60 * 60;
            $secondsPassed = (int) $this->last_login_date->diffInSeconds(now());
            if ($secondsPassed > $threeHours) $secondsPassed = $threeHours;
            $productionInSeconds = $this->production_per_hour / 3600;
            $passiveEarnings = (int) round($productionInSeconds * $secondsPassed);
            $this->increment('balance', $passiveEarnings);
        }
        return $passiveEarnings;
    }

    public function tap($count = 1)
    {
        $taps = min($count, $this->available_energy);
        $multiplier = $this->getActiveBoosterMultiplier();
        $earned = $taps * $this->earn_per_tap * $multiplier;

        $this->balance += $earned;
        $this->available_energy -= $taps;
        $this->last_tap_date = now();
        $this->save();

        // Update tap stats
        DB::transaction(function () use ($taps) {
            $tapStat = TapStat::firstOrCreate(
                ['telegram_user_id' => $this->id],
                ['total_taps' => 0]
            );
            $tapStat->total_taps += $taps;
            $tapStat->save();
        });

        return $earned;
    }

    private function getActiveBoosterMultiplier()
    {
        if ($this->booster_pack_7x) return 7;
        if ($this->booster_pack_3x) return 3;
        if ($this->booster_pack_2x) return 2;
        return 1;
    }

    public function restoreEnergy()
    {
        if ($this->max_energy === $this->available_energy) {
            return 0;
        }

        $now = now();
        $secondsPassed = abs($now->diffInSeconds($this->last_tap_date));

        $maxEnergy = $this->max_energy;

        $energyToRestore = min($secondsPassed, $maxEnergy);

        $this->available_energy = round(min($this->available_energy + $energyToRestore, $maxEnergy));
        $this->last_tap_date = $now;
        $this->save();

        return $energyToRestore;
    }

    public function canUseDailyBooster()
    {
        $now = now();

        // Check if it's a new day
        if (!$this->last_daily_booster_use || $this->last_daily_booster_use->addDay()->lte($now)) {
            return true;
        }

        // Check if an hour has passed since last use and total uses are less than 6
        return $this->daily_booster_uses < 6 && $this->last_daily_booster_use->addHour()->lte($now);
    }

    public function useDailyBooster()
    {
        if (!$this->canUseDailyBooster()) {
            return false;
        }

        $now = now();

        // Reset uses if it's a new day
        if (!$this->last_daily_booster_use || $this->last_daily_booster_use->addDay()->lte($now)) {
            $this->daily_booster_uses = 0;
        }

        $this->daily_booster_uses++;
        $this->last_daily_booster_use = $now;
        $this->available_energy = $this->max_energy; // Regenerate all energy
        $this->save();

        return true;
    }

    public function checkAndResetDailyBooster()
    {
        $now = now();

        if (!$this->last_daily_booster_use || $this->last_daily_booster_use->addDay()->lte($now)) {
            $this->daily_booster_uses = 0;
            $this->last_daily_booster_use = null;
            $this->save();
        }
    }

    /**
     * Check and reset daily plays if needed
     */
    public function checkAndResetPlays()
    {
        if ($this->shouldResetPlays()) {
            $this->tower_game_plays = 15;
            $this->tower_game_plays_reset_at = now();
            $this->save();
        }
    }

    /**
     * Check if plays should be reset
     */
    private function shouldResetPlays()
    {
        return !$this->tower_game_plays_reset_at ||
               $this->tower_game_plays_reset_at->startOfDay()->lt(now()->startOfDay());
    }

    /**
     * Check if user can play tower game
     */
    public function canPlayTowerGame()
    {
        if (!$this->tower_game_unlocked) {
            return ['allowed' => false, 'reason' => 'game_locked'];
        }

        $this->checkAndResetPlays();

        // Check for free plays first
        if ($this->tower_game_plays > 0) {
            return [
                'allowed' => true,
                'free' => true,
                'plays_remaining' => $this->tower_game_plays,
                'balance' => $this->balance
            ];
        }

        // If no free plays, check for paid plays
        if ($this->balance >= 500) {
            return [
                'allowed' => true,
                'free' => false,
                'cost' => 500,
                'plays_remaining' => 0,
                'balance' => $this->balance
            ];
        }

        // No free plays and insufficient balance
        return [
            'allowed' => false,
            'reason' => $this->tower_game_plays <= 0 ? 'no_plays_remaining' : 'insufficient_balance',
            'cost' => 500,
            'plays_remaining' => 0,
            'balance' => $this->balance
        ];
    }

    /**
     * Check if user can play Rabbit game (only checks unlock status)
     */
    public function canPlayRabbitGame()
    {
        // Rabbit game has unlimited plays once unlocked
        if (!$this->rabbit_game_unlocked) {
             return ['allowed' => false, 'reason' => 'game_locked'];
        }
        return ['allowed' => true];
    }

    /**
     * Check if user can play Slash game (only checks unlock status)
     */
    public function canPlaySlashGame()
    {
        // Slash game has unlimited plays once unlocked, following Rabbit game pattern
        if (!$this->slash_game_unlocked) {
             return ['allowed' => false, 'reason' => 'game_locked'];
        }
        return ['allowed' => true];
    }

    /**
     * Use a play for tower game
     */
    public function useTowerGamePlay($paid = false)
    {
        DB::beginTransaction();
        try {
            if ($paid) {
                if ($this->balance < 500) {
                    throw new \Exception('Insufficient balance for paid play');
                }
                $this->balance -= 500;
            } else {
                if ($this->tower_game_plays <= 0) {
                    throw new \Exception('No free plays remaining');
                }
                $this->tower_game_plays--;
            }

            $this->save();
            DB::commit();

            return [
                'success' => true,
                'plays_remaining' => $this->tower_game_plays,
                'balance' => $this->balance
            ];
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
}
