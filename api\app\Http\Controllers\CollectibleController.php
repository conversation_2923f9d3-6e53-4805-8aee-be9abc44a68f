<?php

namespace App\Http\Controllers;

use App\Models\Collectible;
use App\Models\CollectibleTemplate;
use App\Models\CollectionSet;
use App\Models\UserCollectionProgress;
use App\Services\CollectibleService;
use App\Services\AchievementPointService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class CollectibleController extends Controller
{
    protected CollectibleService $collectibleService;
    protected AchievementPointService $achievementPointService;

    public function __construct(
        CollectibleService $collectibleService,
        AchievementPointService $achievementPointService
    ) {
        $this->collectibleService = $collectibleService;
        $this->achievementPointService = $achievementPointService;
    }

    /**
     * Get user's complete collection
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $category = $request->query('category');
        $rarity = $request->query('rarity');
        $owned = $request->query('owned'); // 'true', 'false', or null for all

        $collectibles = $this->collectibleService->getUserCollection(
            $user,
            $category,
            $rarity,
            $owned
        );

        $collectionProgress = $user->getCollectionProgress();
        $collectionSets = $this->collectibleService->getCollectionSets($user);

        return response()->json([
            'success' => true,
            'collectibles' => $collectibles,
            'collection_progress' => $collectionProgress,
            'collection_sets' => $collectionSets,
            'filters' => [
                'categories' => ['shadow', 'undead', 'demon', 'spirit', 'beast'],
                'rarities' => ['common', 'rare', 'epic', 'legendary', 'mythic'],
                'types' => ['artifact', 'trophy', 'relic', 'essence', 'scroll']
            ]
        ]);
    }

    /**
     * Get collection sets with completion status
     */
    public function getCollectionSets(Request $request): JsonResponse
    {
        $user = $request->user();

        $collectionSets = $this->collectibleService->getCollectionSetsDetailed($user);

        return response()->json([
            'success' => true,
            'collection_sets' => $collectionSets
        ]);
    }

    /**
     * Get specific collection set details
     */
    public function getCollectionSet(Request $request, string $setId): JsonResponse
    {
        $user = $request->user();

        $collectionSet = CollectionSet::where('set_id', $setId)
                                     ->where('is_active', true)
                                     ->firstOrFail();

        $setDetails = $this->collectibleService->getCollectionSetDetails($user, $setId);

        return response()->json([
            'success' => true,
            'collection_set' => $setDetails
        ]);
    }

    /**
     * Claim collection set completion rewards
     */
    public function claimSetRewards(Request $request, string $setId): JsonResponse
    {
        $user = $request->user();

        try {
            $result = $this->collectibleService->claimCollectionSetRewards($user, $setId);

            // Award achievement points for set completion
            $this->achievementPointService->awardPoints(
                $user->id,
                50,
                'collection_set_completion',
                $setId,
                "Completed collection set: {$result['set_name']}"
            );

            return response()->json([
                'success' => true,
                'message' => 'Collection set rewards claimed successfully!',
                'rewards' => $result['rewards'],
                'set_name' => $result['set_name'],
                'user_balance' => [
                    'balance' => $user->fresh()->balance,
                    'gems' => $user->fresh()->gems ?? 0
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get collectibles by category
     */
    public function getByCategory(Request $request, string $category): JsonResponse
    {
        $user = $request->user();
        
        $collectibles = $user->collectibles()
                            ->byCategory($category)
                            ->with('template')
                            ->orderBy('obtained_at', 'desc')
                            ->get();
        
        return response()->json([
            'success' => true,
            'category' => $category,
            'collectibles' => $collectibles->map(function($collectible) {
                return [
                    'collectible_id' => $collectible->collectible_id,
                    'name' => $collectible->template->name,
                    'type' => $collectible->template->type,
                    'rarity' => $collectible->template->rarity,
                    'image_url' => $collectible->template->image_url,
                    'obtained_at' => $collectible->obtained_at,
                    'unlock_source' => $collectible->unlock_source
                ];
            }),
            'count' => $collectibles->count()
        ]);
    }

    /**
     * Get collectibles by rarity
     */
    public function getByRarity(Request $request, string $rarity): JsonResponse
    {
        $user = $request->user();
        
        $collectibles = $user->collectibles()
                            ->byRarity($rarity)
                            ->with('template')
                            ->orderBy('obtained_at', 'desc')
                            ->get();
        
        return response()->json([
            'success' => true,
            'rarity' => $rarity,
            'collectibles' => $collectibles->map(function($collectible) {
                return [
                    'collectible_id' => $collectible->collectible_id,
                    'name' => $collectible->template->name,
                    'type' => $collectible->template->type,
                    'category' => $collectible->template->category,
                    'image_url' => $collectible->template->image_url,
                    'obtained_at' => $collectible->obtained_at,
                    'unlock_source' => $collectible->unlock_source
                ];
            }),
            'count' => $collectibles->count()
        ]);
    }

    /**
     * Get recently obtained collectibles
     */
    public function getRecentlyObtained(Request $request): JsonResponse
    {
        $user = $request->user();
        $days = $request->query('days', 7);
        
        $recentCollectibles = $user->collectibles()
                                  ->recentlyObtained($days)
                                  ->with('template')
                                  ->orderBy('obtained_at', 'desc')
                                  ->get();
        
        return response()->json([
            'success' => true,
            'recent_collectibles' => $recentCollectibles->map(function($collectible) {
                return [
                    'collectible_id' => $collectible->collectible_id,
                    'name' => $collectible->template->name,
                    'type' => $collectible->template->type,
                    'category' => $collectible->template->category,
                    'rarity' => $collectible->template->rarity,
                    'image_url' => $collectible->template->image_url,
                    'obtained_at' => $collectible->obtained_at,
                    'unlock_source' => $collectible->unlock_source,
                    'days_ago' => $collectible->obtained_at->diffInDays(now())
                ];
            }),
            'count' => $recentCollectibles->count(),
            'days_filter' => $days
        ]);
    }

    /**
     * Get collection statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $user = $request->user();

        $statistics = $this->collectibleService->getCollectionStatistics($user);

        return response()->json([
            'success' => true,
            'statistics' => $statistics
        ]);
    }

    /**
     * Get collection milestones and achievements
     */
    public function getMilestones(Request $request): JsonResponse
    {
        $user = $request->user();

        $milestones = $this->collectibleService->getCollectionMilestones($user);

        return response()->json([
            'success' => true,
            'milestones' => $milestones
        ]);
    }

    /**
     * Get recently obtained collectibles
     */
    public function getRecentCollectibles(Request $request): JsonResponse
    {
        $user = $request->user();
        $limit = $request->query('limit', 10);

        $recentCollectibles = $user->collectibles()
                                  ->with('collectibleTemplate')
                                  ->orderBy('obtained_at', 'desc')
                                  ->limit($limit)
                                  ->get();

        return response()->json([
            'success' => true,
            'recent_collectibles' => $recentCollectibles->map(function($collectible) {
                return [
                    'id' => $collectible->collectible_id,
                    'name' => $collectible->collectibleTemplate->name,
                    'type' => $collectible->collectibleTemplate->type,
                    'rarity' => $collectible->collectibleTemplate->rarity,
                    'category' => $collectible->collectibleTemplate->category,
                    'image_url' => $collectible->collectibleTemplate->image_url,
                    'obtained_at' => $collectible->obtained_at,
                    'unlock_source' => $collectible->unlock_source,
                    'days_ago' => $collectible->obtained_at->diffInDays(now())
                ];
            })
        ]);
    }

    /**
     * Search collectibles
     */
    public function search(Request $request): JsonResponse
    {
        $user = $request->user();
        $query = $request->query('q');

        if (!$query || strlen($query) < 2) {
            return response()->json([
                'success' => false,
                'message' => 'Search query must be at least 2 characters'
            ], 422);
        }

        $results = $this->collectibleService->searchCollectibles($user, $query);

        return response()->json([
            'success' => true,
            'search_query' => $query,
            'results' => $results
        ]);
    }
}
