<?php

namespace App\Services;

use App\Models\TelegramUser;
use App\Models\AchievementPointTransaction;
use App\Models\UserAchievementPoint;
use Illuminate\Support\Facades\DB;

class AchievementPointService
{
    /**
     * Award achievement points to a user
     */
    public function awardPoints(
        int $userId,
        int $points,
        string $category,
        ?string $referenceId = null,
        ?string $description = null
    ): AchievementPointTransaction {
        $user = TelegramUser::findOrFail($userId);

        DB::beginTransaction();
        try {
            // Create achievement point transaction
            $transaction = AchievementPointTransaction::create([
                'telegram_user_id' => $userId,
                'amount' => $points,
                'type' => 'earned',
                'source' => $category,
                'source_id' => $referenceId,
                'description' => $description
            ]);

            // Update or create user achievement points record
            $userAchievementPoints = UserAchievementPoint::firstOrCreate(
                ['telegram_user_id' => $userId],
                ['total_earned' => 0, 'total_spent' => 0]
            );

            $userAchievementPoints->increment('total_earned', $points);

            // Update user's total achievement points for quick access
            $user->increment('achievement_points', $points);

            DB::commit();

            return $transaction;

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get user's achievement point history
     */
    public function getUserHistory(TelegramUser $user, int $limit = 50): array
    {
        $history = AchievementPointTransaction::where('telegram_user_id', $user->id)
                                             ->where('type', 'earned')
                                             ->orderBy('created_at', 'desc')
                                             ->limit($limit)
                                             ->get();

        return $history->map(function($record) {
            return [
                'id' => $record->id,
                'points' => $record->amount,
                'category' => $record->source,
                'description' => $record->description,
                'awarded_at' => $record->created_at,
                'days_ago' => $record->created_at->diffInDays(now())
            ];
        })->toArray();
    }

    /**
     * Get user's achievement point statistics
     */
    public function getUserStatistics(TelegramUser $user): array
    {
        $userAchievementPoints = UserAchievementPoint::where('telegram_user_id', $user->id)->first();
        $totalPoints = $userAchievementPoints ? $userAchievementPoints->available_points : 0;

        $pointsByCategory = AchievementPointTransaction::where('telegram_user_id', $user->id)
                                                      ->where('type', 'earned')
                                                      ->selectRaw('source as category, SUM(amount) as total_points, COUNT(*) as count')
                                                      ->groupBy('source')
                                                      ->get()
                                                      ->keyBy('category')
                                                      ->map(function($item) {
                                                          return [
                                                              'total_points' => $item->total_points,
                                                              'count' => $item->count
                                                          ];
                                                      })
                                                      ->toArray();

        $recentPoints = AchievementPointTransaction::where('telegram_user_id', $user->id)
                                                  ->where('type', 'earned')
                                                  ->where('created_at', '>=', now()->subDays(7))
                                                  ->sum('amount');

        return [
            'total_points' => $totalPoints,
            'total_earned' => $userAchievementPoints ? $userAchievementPoints->total_earned : 0,
            'total_spent' => $userAchievementPoints ? $userAchievementPoints->total_spent : 0,
            'points_by_category' => $pointsByCategory,
            'recent_points_7_days' => $recentPoints,
            'total_awards' => AchievementPointTransaction::where('telegram_user_id', $user->id)
                                                        ->where('type', 'earned')
                                                        ->count()
        ];
    }

    /**
     * Check if user has already received points for a specific reference
     */
    public function hasReceivedPoints(int $userId, string $category, string $referenceId): bool
    {
        return AchievementPointTransaction::where([
            'telegram_user_id' => $userId,
            'source' => $category,
            'source_id' => $referenceId,
            'type' => 'earned'
        ])->exists();
    }

    /**
     * Award points only if not already awarded for the same reference
     */
    public function awardPointsOnce(
        int $userId,
        int $points,
        string $category,
        string $referenceId,
        ?string $description = null
    ): ?AchievementPointTransaction {
        if ($this->hasReceivedPoints($userId, $category, $referenceId)) {
            return null;
        }

        return $this->awardPoints($userId, $points, $category, $referenceId, $description);
    }
}
